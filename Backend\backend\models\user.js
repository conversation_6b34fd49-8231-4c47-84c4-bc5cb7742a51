const pool = require('../config/db');
const bcrypt = require('bcrypt');

class User {
  static async findOne(condition) {
    try {
      let query, params;
      
      if (condition.email) {
        query = 'SELECT * FROM users WHERE email = ? LIMIT 1';
        params = [condition.email]; 
      } else if (condition.id) {
        query = 'SELECT * FROM users WHERE id = ? LIMIT 1';
        params = [condition.id];
      } else {
        return null;
      }
      
      const [rows] = await pool.execute(query, params);
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error('Error finding user:', error);
      throw error;
    }
  }
  
  static async findById(id) {
    return this.findOne({ id });
  }
  
  static async create(userData) {
    try {
      
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      
      const query = `
        INSERT INTO users (email, name_account, password, date_of_birth, address) 
        VALUES (?, ?, ?, ?, ?)
      `;
      
      const [result] = await pool.execute(query, [
        userData.email,
        userData.name_account,
        hashedPassword,
        userData.date_of_birth,
        userData.address
      ]);
      
      if (result.insertId) {
        return {
          _id: result.insertId,
          ...userData,
          password: undefined
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }
  
  static async comparePassword(user, candidatePassword) {
    return await bcrypt.compare(candidatePassword, user.password);
  }

  static async updateInfo(userId, updateData) {
    try {
      

      const allowedFields = ['name_account', 'email', 'date_of_birth', 'address', 'search_distance'];
      const updateFields = [];
      const values = [];

      let hashedPassword;
      if (updateData.password) {
        hashedPassword = await bcrypt.hash(updateData.password, 10);
        updateFields.push('password = ?');
        values.push(hashedPassword);
      }

      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          updateFields.push(`${field} = ?`);
          values.push(updateData[field]);
        }
      }

      if (updateFields.length === 0) {
        return { updated: false, message: 'No fields to update' };
      }

      values.push(userId);
      
      const query = `
        UPDATE users 
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;
      
      const [result] = await pool.execute(query, values);
      
      if (result.affectedRows > 0) {
        // Get updated user data
        const updatedUser = await this.findById(userId);
        return {
          updated: true,
          user: {
            ...updatedUser,
            password: undefined
          }
        };
      }
      
      return { updated: false, message: 'User not found or no changes made' };
    } catch (error) {
      console.error('Error updating user:', error);
      console.log(error)
      throw error;
    }
  }
}

module.exports = User;