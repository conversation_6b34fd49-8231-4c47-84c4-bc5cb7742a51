import 'package:shared_preferences/shared_preferences.dart';
import 'package:geocoding/geocoding.dart';
import 'user_preferences_utils.dart';

class LocationPreferencesUtils {
  // Keys for SharedPreferences - use the ones from UserPreferencesUtils
  static const String LAT_KEY = UserPreferencesUtils.USER_LAT_KEY;
  static const String LON_KEY = UserPreferencesUtils.USER_LON_KEY;
  static const String ADDRESS_KEY = UserPreferencesUtils.USER_ADDRESS_KEY;
  static const String LAST_UPDATE_KEY = 'location_last_update';

  // Load saved location from SharedPreferences
  static Future<Map<String, dynamic>> loadSavedLocation() async {
    final prefs = await SharedPreferences.getInstance();

    final double? lat = prefs.getDouble(LAT_KEY);
    final double? lon = prefs.getDouble(LON_KEY);
    final String address = prefs.getString(ADDRESS_KEY) ?? 'Getting location...';

    // Parse last update time
    DateTime? lastUpdate;
    final lastUpdateString = prefs.getString(LAST_UPDATE_KEY);
    if (lastUpdateString != null) {
      lastUpdate = DateTime.parse(lastUpdateString);
    }

    return {
      'latitude': lat,
      'longitude': lon,
      'address': address,
      'lastUpdate': lastUpdate,
    };
  }

  static Future<void> saveLocation(double lat, double lon, String address) async {
    final prefs = await SharedPreferences.getInstance();

    // Update location in both our systems
    await prefs.setDouble(LAT_KEY, lat);
    await prefs.setDouble(LON_KEY, lon);
    await prefs.setString(ADDRESS_KEY, address);

    // Also update in UserPreferencesUtils system
    await UserPreferencesUtils.updateUserProfileField(LAT_KEY, lat);
    await UserPreferencesUtils.updateUserProfileField(LON_KEY, lon);
    await UserPreferencesUtils.updateUserProfileField(ADDRESS_KEY, address);

    final now = DateTime.now();
    await prefs.setString(LAST_UPDATE_KEY, now.toIso8601String());
  }

  static Future<String> getAddressFromCoordinates(double lat, double lon) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(lat, lon);

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];

        String address = '';

        if ((place.street?.isNotEmpty ?? false)) {
          address += place.street!;
        }

        // Add subLocality (neighborhood/district)
        if (place.subLocality?.isNotEmpty ?? false) {
          if (address.isNotEmpty) address += ', ';
          address += place.subLocality!;
        }

        // Add locality (city)
        if (place.locality?.isNotEmpty ?? false) {
          if (address.isNotEmpty) address += ', ';
          address += place.locality!;
        }

        // Add administrative area (state/province)
        if (place.administrativeArea?.isNotEmpty ?? false) {
          if (address.isNotEmpty) address += ', ';
          address += place.administrativeArea!;
        }

        // Add country
        if (place.country?.isNotEmpty ?? false) {
          if (address.isNotEmpty) address += ', ';
          address += place.country!;
        }

        return address.isNotEmpty ? address : 'Unknown location';
      }
    } catch (e) {
      print("Error getting address: $e");
    }

    return 'Unknown location';
  }
}