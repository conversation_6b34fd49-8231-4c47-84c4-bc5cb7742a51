import 'package:flutter/material.dart';
import '../constants/colors.dart';
import '../models/comment.dart';

class CommentItem extends StatelessWidget {
  final Comment comment;

  const CommentItem({
    Key? key,
    required this.comment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Gray.border, width: 0.5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info and date
          Row(
            children: [
              // Profile picture
              CircleAvatar(
                radius: 16,
                backgroundImage: _getImageProvider(comment.userAvatar),
                backgroundColor: Colors.grey[300],
              ),
              const SizedBox(width: 8),

              // Username
              Expanded(
                child: Text(
                  comment.userName,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    color: AppColors.black,
                  ),
                ),
              ),

              // Date
              Text(
                comment.date,
                style: TextStyle(
                  fontSize: 12,
                  color: Gray.text,
                ),
              ),
            ],
          ),

          // Comment content
          Padding(
            padding: const EdgeInsets.only(top: 8, left: 40),
            child: Text(
              comment.content,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.black,
              ),
            ),
          ),

          // Post reference (if any)
          if (comment.postTitle != null && comment.postTitle!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8, left: 40),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.article_outlined, size: 14, color: Gray.text),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'on post: ${comment.postTitle}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Gray.text,
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Helper method to handle different image sources
  ImageProvider _getImageProvider(String imageSource) {
    if (imageSource.startsWith('http')) {
      return NetworkImage(imageSource);
    } else {
      return AssetImage(imageSource);
    }
  }
}