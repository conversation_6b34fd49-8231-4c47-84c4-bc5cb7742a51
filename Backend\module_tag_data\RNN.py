import torch
import torch.nn as nn
import torch.nn.functional as F

class RNN(nn.Module):
    def __init__(self, vocab_size, embedding_dim, hidden_dim, n_layers, 
                 bidirectional, dropout, pad_idx):
        super().__init__()
        
        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx=pad_idx)
        
        # Freeze embedding initially
        self.embedding.weight.requires_grad = False
        
        # Configure LSTM with regularization
        self.rnn = nn.LSTM(embedding_dim, 
                           hidden_dim, 
                           num_layers=n_layers, 
                           bidirectional=bidirectional, 
                           dropout=dropout,
                           batch_first=False)
        
        # Add weight regularization to linear layers
        self.fc1 = nn.utils.weight_norm(nn.Linear(hidden_dim * 2, hidden_dim))
        self.fc2 = nn.utils.weight_norm(nn.Linear(hidden_dim, hidden_dim // 2))
        self.fc3 = nn.Linear(hidden_dim // 2, 1)
        
        # Enhanced dropout and normalization
        self.dropout = nn.Dropout(dropout)
        self.batch_norm1 = nn.BatchNorm1d(hidden_dim)
        self.batch_norm2 = nn.BatchNorm1d(hidden_dim // 2)
        
        # Layer-wise weight initialization
        self.init_weights()
        
    def init_weights(self):
        # Xavier/Glorot initialization for linear layers
        nn.init.xavier_uniform_(self.fc1.weight)
        nn.init.xavier_uniform_(self.fc2.weight)
        nn.init.xavier_uniform_(self.fc3.weight)
        
    def forward(self, text, text_lengths):
        # Dynamic embedding unfreeze strategy
        if self.training and torch.rand(1).item() > 0.5:
            self.embedding.weight.requires_grad = True
        
        embedded = self.dropout(self.embedding(text))
        
        packed_embedded = nn.utils.rnn.pack_padded_sequence(embedded, text_lengths.to('cpu'))
        packed_output, (hidden, cell) = self.rnn(packed_embedded)
        
        # Concat the final forward and backward hidden states
        hidden = torch.cat((hidden[-2,:,:], hidden[-1,:,:]), dim=1)
        hidden = self.dropout(hidden)
        
        hidden = self.fc1(hidden)
        hidden = F.leaky_relu(hidden)  # Replace ReLU with Leaky ReLU
        hidden = self.batch_norm1(hidden)
        hidden = self.dropout(hidden)
        
        hidden = self.fc2(hidden)
        hidden = F.leaky_relu(hidden)
        hidden = self.batch_norm2(hidden)
        hidden = self.dropout(hidden)
        
        return self.fc3(hidden)