import pymysql
from RecommendationSystem import RecommendationSystem
from flask import Flask, Blueprint, jsonify
from config import get_mysql_config
from database import DatabaseManager
import time
from dotenv import load_dotenv
import mysql.connector
from flask_cors import CORS
from flask import request
load_dotenv()

app = Flask(__name__)
app.debug = True
CORS(app) 
MYSQL_CONFIG = get_mysql_config()
db_manager = DatabaseManager(MYSQL_CONFIG)

api_v1 = Blueprint('api_v1', __name__, url_prefix='/api/v1')

@api_v1.route('/recommend', methods=['GET'])
def recommend():
    try:
        user_id = request.args.get('user_id', type=int)
        lat = request.args.get('lat', type=float)
        lon = request.args.get('lon', type=float)
        distance_km = request.args.get('distance_km', default=10, type=float)
        method = request.args.get('method', default='location', type=str)
        page = request.args.get('page', default=1, type=int)
        limit = request.args.get('limit', default=10, type=int)
        
        if page < 1:
            page = 1
        if limit < 1 or limit > 50: 
            limit = 10
            
        db_connection = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='funplay'
        )

        recommender = RecommendationSystem(db_connection)
        recommender.load_data()

        all_recommendations = recommender.recommend(
            user_id=user_id, 
            lat=lat, 
            lon=lon, 
            distance_km=distance_km,
            method=method
        )
        
        if all_recommendations.empty:
            return jsonify({
                "status": "success",
                "data": [],
                "message": f"No {method} recommendations found."
            })

        offset = (page - 1) * limit
        paginated_recommendations = all_recommendations.iloc[offset:offset+limit]

        total_records = len(all_recommendations)
        total_pages = (total_records + limit - 1) // limit
        
        return jsonify({
            "status": "success",
            "data": paginated_recommendations.to_dict('records'),
            "pagination": {
                "page": page,
                "limit": limit,
                "total_records": total_records,
                "total_pages": total_pages
            },
            "message": f"Successfully retrieved {method} recommendations (page {page}/{total_pages})."
        })
        
    except mysql.connector.Error as err:
        print(f"Database error: {err}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(err)}"
        }), 500
    except Exception as e:
        print(f"Unexpected error: {e}")
        return jsonify({
            "status": "error",
            "message": f"Unexpected error: {str(e)}"
        }), 500
          
app.register_blueprint(api_v1)
      
if __name__ == "__main__": 
    app.run(host="0.0.0.0", port=5020, debug=True)