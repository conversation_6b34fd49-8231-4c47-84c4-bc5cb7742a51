class SubComment {
  final String id;
  final String parentCommentId;
  final String message;
  final String createdTime;
  final String status;
  final double rate;
  final int isTaggedSentiment;
  final String? userName;
  final String userAvatar;
  final String? postId;
  final String? postTitle;

  SubComment({
    required this.id,
    required this.parentCommentId,
    required this.message,
    required this.createdTime,
    this.status = '',
    this.rate = 0.0,
    this.isTaggedSentiment = 0,
    this.userName,
    this.userAvatar = 'assets/images/default_avatar.png',
    this.postId,
    this.postTitle,
  });

  factory SubComment.fromJson(Map<String, dynamic> json) {
    return SubComment(
      id: json['id']?.toString() ?? '',
      parentCommentId: json['parent_comment_id']?.toString() ?? '',
      message: json['message'] ?? '',
      createdTime: json['created_time'] ?? '',
      status: json['status'] ?? '',
      // Fixed: handle both int and double values
      rate: json['rate'] != null
          ? (json['rate'] is int)
          ? (json['rate'] as int).toDouble()
          : (json['rate'] is double)
          ? json['rate']
          : double.tryParse(json['rate'].toString()) ?? 0.0
          : 0.0,
      isTaggedSentiment: json['isTaggedSentiment'] ?? 0,
      userName: json['user_name'] ?? 'User',
      userAvatar: json['user_avatar'] ?? 'assets/images/default_avatar.png',
      postId: json['post_id'],
      postTitle: json['post_title'],
    );
  }
}
