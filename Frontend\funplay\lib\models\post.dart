// models/post.dart
import '../constants/images.dart';
import 'package:html/parser.dart' as htmlparser;

class Post {
  final String id;
  final String title;
  final String location;
  final double rating;
  final int comments;
  final int reactions;
  final String image;
  final double? latitude;
  final double? longitude;
  final String createdTime;
  bool isFavorite;
  final String? description;

  Post({
    required this.id,
    required this.title,
    required this.location,
    required this.rating,
    required this.comments,
    required this.reactions,
    required this.image,
    this.latitude,
    this.longitude,
    this.createdTime = '',
    this.isFavorite = false,
    this.description,
  });

  factory Post.fromJson(Map<String, dynamic> json) {
    try {
      // Ensure we have an ID - most critical field
      if (json['id'] == null) {
        throw Exception('Post data missing ID field');
      }

      // Extract title from message
      String message = json['message'] ?? '';
      String title = '';
      String cleanedMessage = message;

      // Parse HTML content if present
      if (message.contains('<br />')) {
        var document = htmlparser.parse(message);
        cleanedMessage = document.body!.text;

        // Get first line as title
        List<String> lines = cleanedMessage.split('\n');
        if (lines.isNotEmpty) {
          title = lines[0].trim();
          // If title is too short, use first two lines
          if (title.length < 10 && lines.length > 1) {
            title = '$title - ${lines[1].trim()}';
          }
        }
      } else {
        // If no HTML, just take the first part as title
        List<String> parts = message.split('\n');
        if (parts.isNotEmpty) {
          title = parts[0].trim();
        }
      }

      // If still no title, use a default with the ID
      if (title.isEmpty) {
        title = 'Place ${json['id']?.toString().substring(0, 5) ?? ''}';
      }

      // Get location - handle null safely
      String location = json['address'] ?? '';

      // Safely handle numerical values with null checks and type conversion
      double rating = 0.0;
      if (json['rate'] != null) {
        if (json['rate'] is int) {
          rating = (json['rate'] as int).toDouble();
        } else if (json['rate'] is double) {
          rating = json['rate'];
        } else {
          rating = double.tryParse(json['rate'].toString()) ?? 0.0;
        }
      }

      // Safely parse latitude and longitude
      double? latitude;
      if (json['latitude'] != null) {
        if (json['latitude'] is int) {
          latitude = (json['latitude'] as int).toDouble();
        } else if (json['latitude'] is double) {
          latitude = json['latitude'];
        } else {
          latitude = double.tryParse(json['latitude'].toString());
        }
      }

      double? longitude;
      if (json['longitude'] != null) {
        if (json['longitude'] is int) {
          longitude = (json['longitude'] as int).toDouble();
        } else if (json['longitude'] is double) {
          longitude = json['longitude'];
        } else {
          longitude = double.tryParse(json['longitude'].toString());
        }
      }

      return Post(
        id: json['id'].toString(),
        title: title,
        location: location,
        rating: rating,
        comments: json['comments_count'] ?? 0,
        reactions: json['reactions_total_count'] ?? 0,
        image: json['full_picture'] ?? AppImages.postDetailImage,
        latitude: latitude,
        longitude: longitude,
        createdTime: json['created_time'] ?? '',
        isFavorite: json['isFavorite'] ?? false,
        description: message,
      );
    } catch (e) {
      print("Error parsing Post from JSON: $e");
      print("Problematic JSON: $json");
      rethrow; // Re-throw to be caught by _fetchWithRetry's error handling
    }
  }

  // Dummy data for nearby posts
  factory Post.dummyNearby(int index) {
    return Post(
      id: 'dummy_nearby_$index',
      title: 'Địa điểm gần bạn $index',
      location: 'Quận Cầu Giấy, Hà Nội',
      rating: 4.5,
      comments: 12,
      reactions: 45,
      image: AppImages.postDetailImage,
      latitude: 21.0832018 + (index * 0.001),
      longitude: 105.8959069 + (index * 0.001),
      description:
      'Đây là một địa điểm gần vị trí của bạn. Rất phù hợp để đi cùng bạn bè vào cuối tuần.',
    );
  }

  // Dummy data for trending posts
  factory Post.dummyTrending(int index) {
    return Post(
      id: 'dummy_trending_$index',
      title: 'Địa điểm đang hot $index',
      location: 'Quận Hoàn Kiếm, Hà Nội',
      rating: 4.8,
      comments: 45,
      reactions: 120,
      image: AppImages.postDetailImage,
      description:
      'Địa điểm đang được nhiều người quan tâm trong thời gian gần đây. Có nhiều ưu đãi hấp dẫn.',
    );
  }

  // Dummy data for personalized posts
  factory Post.dummyPersonalized(int index) {
    return Post(
      id: 'dummy_personalized_$index',
      title: 'Gợi ý cho bạn $index',
      location: 'Quận Tây Hồ, Hà Nội',
      rating: 4.6,
      comments: 23,
      reactions: 67,
      image: AppImages.postDetailImage,
      description:
      'Dựa trên sở thích của bạn, đây là địa điểm phù hợp mà bạn có thể sẽ thích.',
    );
  }
}