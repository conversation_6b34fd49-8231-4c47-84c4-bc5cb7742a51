import 'package:flutter/material.dart';
import '../constants/images.dart';

class CommentModel {
  final String id;
  final String postId;
  final String createdTime;
  final String message;
  final String status;
  final double rate;
  final int reactionsCount;
  final int commentCount;
  final bool isTaggedSentiment;
  final String? userName;
  final List<CommentModel> subComments;

  CommentModel({
    required this.id,
    required this.postId,
    required this.createdTime,
    required this.message,
    required this.status,
    required this.rate,
    required this.reactionsCount,
    required this.commentCount,
    required this.isTaggedSentiment,
    this.userName,
    required this.subComments,
  });

  factory CommentModel.fromJson(Map<String, dynamic> json) {
    List<CommentModel> subComments = [];
    if (json['sub_comments'] != null) {
      subComments = List<CommentModel>.from(
        (json['sub_comments'] as List).map((comment) => CommentModel.fromJson(comment)),
      );
    }

    return CommentModel(
      id: json['id']?.toString() ?? '',
      postId: json['post_id']?.toString() ?? '',
      createdTime: json['created_time'] ?? '',
      message: json['message'] ?? '',
      status: json['status'] ?? '',
      rate: _parseRate(json['rate']),
      reactionsCount: json['reactions_total_count'] ?? 0,
      commentCount: json['comment_count'] ?? 0,
      isTaggedSentiment: json['isTaggedSentiment'] == 1,
      userName: json['user_name'],
      subComments: subComments,
    );
  }

  static double _parseRate(dynamic rate) {
    if (rate == null) return 0.0;

    if (rate is double) return rate;
    if (rate is int) return rate.toDouble();

    try {
      return double.parse(rate.toString());
    } catch (e) {
      return 0.0;
    }
  }

  String getRatingCategory() {
    if (status == 'excellent') return 'Excellent';
    if (status == 'good') return 'Good';
    if (status == 'normal') return 'Normal';
    return 'Bad';
  }

  String getRatingEmoji() {
    if (status == 'excellent' || status == 'good') return "😊";
    if (status == 'normal') return "😐";
    return "😟";
  }
}

class PostDetail {
  final String id;
  final String title;
  final String location;
  final double rate;
  final int sharesCount;
  final int reactions;
  final String fullPicture;
  final double? latitude;
  final double? longitude;
  final String createdTime;
  final String? permalinkUrl;
  final String? message;
  final List<ImageModel> images;
  final List<SubImageModel> subImages;
  final List<dynamic> commentsList; // Keep as dynamic for now
  bool isFavorite;

  PostDetail({
    required this.id,
    required this.title,
    required this.location,
    required this.rate,
    required this.sharesCount,
    required this.reactions,
    required this.fullPicture,
    this.latitude,
    this.longitude,
    required this.createdTime,
    this.permalinkUrl,
    this.message,
    required this.images,
    required this.subImages,
    required this.commentsList,
    this.isFavorite = false,
  });

  factory PostDetail.fromJson(Map<String, dynamic> json) {
    // Extract title from message
    String message = json['message'] ?? '';
    String title = '';

    // Parse message and extract title
    if (message.contains('<br />')) {
      var parts = message.split('<br />');
      title = parts[0].trim();
    } else {
      var parts = message.split('\n');
      title = parts[0].trim();
    }

    // If title is empty, use a default
    if (title.isEmpty) {
      title = 'Post ${json['id']?.toString().substring(0, 5) ?? ''}';
    }

    // Get location
    String location = json['address'] ?? '';

    // Parse images
    List<ImageModel> images = [];
    if (json['images'] != null) {
      images = List<ImageModel>.from(
        json['images'].map((img) => ImageModel.fromJson(img)),
      );
    }

    // Parse sub-images
    List<SubImageModel> subImages = [];
    if (json['sub_images'] != null) {
      subImages = List<SubImageModel>.from(
        json['sub_images'].map((img) => SubImageModel.fromJson(img)),
      );
    }

    // Fixed: Properly parse latitude and longitude to handle both int and double values
    double? latitude;
    if (json['latitude'] != null) {
      latitude = json['latitude'] is int ?
      (json['latitude'] as int).toDouble() :
      (json['latitude'] as double);
    }

    double? longitude;
    if (json['longitude'] != null) {
      longitude = json['longitude'] is int ?
      (json['longitude'] as int).toDouble() :
      (json['longitude'] as double);
    }

    // Fixed: Properly parse rate to handle both int and double values
    double rate = 0.0;
    if (json['rate'] != null) {
      rate = json['rate'] is int ?
      (json['rate'] as int).toDouble() :
      (json['rate'] is double ? json['rate'] : double.tryParse(json['rate'].toString()) ?? 0.0);
    }

    return PostDetail(
      id: json['id']?.toString() ?? '',
      title: title,
      location: location,
      rate: rate,
      sharesCount: json['shares_count'] ?? 0,
      reactions: json['reactions_total_count'] ?? 0,
      fullPicture: json['full_picture'] ?? AppImages.postDetailImage,
      latitude: latitude,
      longitude: longitude,
      createdTime: json['created_time'] ?? '',
      permalinkUrl: json['permalink_url'],
      message: message,
      images: images,
      subImages: subImages,
      commentsList: json['comments'] ?? [],
      isFavorite: false,
    );
  }

  // Get count of comments including sub-comments
  int get comments {
    int count = commentsList.length;
    for (var comment in commentsList) {
      if (comment['sub_comments'] != null) {
        count += (comment['sub_comments'] as List).length;
      }
    }
    return count;
  }

  // Get all image URLs for slider
  List<String> getImageUrls() {
    List<String> urls = [];

    // Add main image if it exists
    if (fullPicture.isNotEmpty) {
      urls.add(fullPicture);
    }

    // Add all other images
    for (var img in images) {
      if (img.url.isNotEmpty && !urls.contains(img.url)) {
        urls.add(img.url);
      }
    }

    // Add all sub-images
    for (var img in subImages) {
      if (img.imageSrc.isNotEmpty && !urls.contains(img.imageSrc)) {
        urls.add(img.imageSrc);
      }
    }

    // If no images, add default
    if (urls.isEmpty) {
      urls.add(AppImages.postDetailImage);
    }

    return urls;
  }

  // Count comments by rating category
  Map<String, int> getCommentRatingBreakdown() {
    Map<String, int> breakdown = {
      "excellent": 0,
      "good": 0,
      "normal": 0,
      "bad": 0,
    };

    // Count main comments
    for (var comment in commentsList) {
      String status = comment['status'] ?? 'normal';
      breakdown[status] = (breakdown[status] ?? 0) + 1;

      // Count sub-comments
      List<dynamic> subComments = comment['sub_comments'] ?? [];
      for (var subComment in subComments) {
        String subStatus = subComment['status'] ?? 'normal';
        breakdown[subStatus] = (breakdown[subStatus] ?? 0) + 1;
      }
    }

    return breakdown;
  }

  // Dummy data for testing
  factory PostDetail.dummyTrending(int index, {required int ratingScale}) {
    return PostDetail(
      id: 'dummy_trending_$index',
      title: 'Địa điểm đang hot $index',
      location: 'Quận Hoàn Kiếm, Hà Nội',
      rate: 4.8,
      sharesCount: 15,
      reactions: 120,
      fullPicture: AppImages.postDetailImage,
      createdTime: '2024-11-04T20:16:29.000Z',
      message: 'Địa điểm đang được nhiều người quan tâm trong thời gian gần đây. Có nhiều ưu đãi hấp dẫn.',
      images: [],
      subImages: [],
      commentsList: [],
    );
  }
}

class ImageModel {
  final int id;
  final String postId;
  final String url;

  ImageModel({
    required this.id,
    required this.postId,
    required this.url,
  });

  factory ImageModel.fromJson(Map<String, dynamic> json) {
    return ImageModel(
      id: json['id'] ?? 0,
      postId: json['post_id']?.toString() ?? '',
      url: json['full_picture_src'] ?? json['image_src'] ?? '',
    );
  }
}

class SubImageModel {
  final int id;
  final String postId;
  final int imageHeight;
  final int imageWidth;
  final String imageSrc;
  final String targetId;
  final String targetUrl;
  final String type;

  SubImageModel({
    required this.id,
    required this.postId,
    required this.imageHeight,
    required this.imageWidth,
    required this.imageSrc,
    required this.targetId,
    required this.targetUrl,
    required this.type,
  });

  factory SubImageModel.fromJson(Map<String, dynamic> json) {
    return SubImageModel(
      id: json['id'] ?? 0,
      postId: json['post_id']?.toString() ?? '',
      imageHeight: json['image_height'] ?? 0,
      imageWidth: json['image_width'] ?? 0,
      imageSrc: json['image_src'] ?? '',
      targetId: json['target_id']?.toString() ?? '',
      targetUrl: json['target_url'] ?? '',
      type: json['type'] ?? '',
    );
  }
}