import 'package:flutter/material.dart';
import '../constants/colors.dart';
import '../constants/images.dart';
import '../models/post.dart';
import '../api/favorite_service.dart';
import 'package:funplay/screens/post_detail_screen.dart';

class PostList extends StatefulWidget {
  final String type;
  final List<Post> data;
  final ScrollController? scrollController;
  final Function(Post, bool)? onFavoriteToggled;

  const PostList({
    Key? key,
    this.type = "nearMe",
    required this.data,
    this.scrollController,
    this.onFavoriteToggled,
  }) : super(key: key);

  @override
  State<PostList> createState() => _PostListState();
}

class _PostListState extends State<PostList> {
  // Track posts that are currently being processed for favorite toggling
  // to prevent multiple rapid taps
  Set<String> _processingFavorites = {};

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: widget.scrollController,
      itemCount: widget.data.length,
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        final post = widget.data[index];
        return _buildPostItem(context, post);
      },
    );
  }

  Widget _buildPostItem(BuildContext context, Post post) {
    return InkWell(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PostDetailScreen(post: post),
          ),
        );
      },
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: _buildImage(post.image),
                  ),
                  const SizedBox(width: 12),

                  // Post text content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          post.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.black,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),

                        // Location
                        Row(
                          children: [
                            Image.asset(
                              AppImages.location,
                              width: 14,
                              height: 14,
                              color: const Color(0xFFF4900C),
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                post.location,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Gray.text,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),

                        // Stats - Fixed layout to prevent overflow
                        Row(
                          children: [
                            // Rating
                            Image.asset(
                              AppImages.star,
                              width: 14,
                              height: 14,
                              color: const Color(0xFFF4900C),
                            ),
                            const SizedBox(width: 2),
                            Text(
                              post.rating.toString(),
                              style: TextStyle(
                                fontSize: 12,
                                color: Gray.text,
                              ),
                            ),
                            _buildDivider(),

                            // Comments
                            Flexible(
                              child: Text(
                                '${post.comments} comments',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Gray.text,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            _buildDivider(),

                            // Reactions
                            Flexible(
                              child: Text(
                                '${post.reactions} reacts',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Gray.text,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Favorite button with toggle functionality
                  GestureDetector(
                    onTap: () {
                      _toggleFavorite(post);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: _processingFavorites.contains(post.id)
                          ? SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: AppColors.primary,
                        ),
                      )
                          : Image.asset(
                        post.isFavorite ? AppImages.heartFilled : AppImages.heart,
                        width: 24,
                        height: 24,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Divider
          Container(
            height: 1,
            color: Gray.border,
          ),
        ],
      ),
    );
  }

  Future<void> _toggleFavorite(Post post) async {
    // Prevent multiple rapid taps
    if (_processingFavorites.contains(post.id)) {
      return;
    }

    setState(() {
      _processingFavorites.add(post.id);
    });

    try {
      final newFavoriteStatus = await FavoriteService.toggleFavorite(post);

      setState(() {
        post.isFavorite = newFavoriteStatus;
        _processingFavorites.remove(post.id);
      });

      // Notify parent if callback provided
      if (widget.onFavoriteToggled != null) {
        widget.onFavoriteToggled!(post, newFavoriteStatus);
      }
    } catch (e) {
      setState(() {
        _processingFavorites.remove(post.id);
      });

      // Show error snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating favorite status: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Helper method to handle different image sources
  Widget _buildImage(String imageSource) {
    // Check if the imageSource is a URL or an asset path
    if (imageSource.startsWith('http')) {
      return Image.network(
        imageSource,
        width: 60,
        height: 60,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 60,
            height: 60,
            color: Colors.grey[300],
            child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
          );
        },
      );
    } else {
      return Image.asset(
        imageSource,
        width: 60,
        height: 60,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 60,
            height: 60,
            color: Colors.grey[300],
            child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
          );
        },
      );
    }
  }

  Widget _buildDivider() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Text(
        '|',
        style: TextStyle(
          fontSize: 12,
          color: Gray.light,
        ),
      ),
    );
  }
}