import 'dart:math';
import 'package:flutter/material.dart';
import '../constants/images.dart';
import '../screens/search_screen.dart'; // Import the SearchScreen

class WheelOption {
  final int id;
  final String label;
  final Color color;
  final double probability;

  WheelOption({
    required this.id,
    required this.label,
    required this.color,
    required this.probability,
  });
}

class SpinScreen extends StatefulWidget {
  const SpinScreen({Key? key}) : super(key: key);

  @override
  State<SpinScreen> createState() => _SpinScreenState();
}

class _SpinScreenState extends State<SpinScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isSpinning = false;
  WheelOption? _result;
  bool _showResultModal = false;
  double _finalRotation = 0;

  final List<WheelOption> _wheelOptions = [
    WheelOption(id: 1, label: "Bún", color: Color(0xFFE57373), probability: 2),
    WheelOption(id: 2, label: "Phở", color: Color(0xFFF06292), probability: 2),
    WheelOption(
        id: 3, label: "Bánh mì", color: Color(0xFFBA68C8), probability: 2),
    WheelOption(
        id: 4, label: "Sushi", color: Color(0xFF9575CD), probability: 2),
    WheelOption(id: 5, label: "Cơm", color: Color(0xFF7986CB), probability: 2),
    WheelOption(
        id: 6, label: "Buffet", color: Color(0xFF64B5F6), probability: 2),
    WheelOption(id: 7, label: "Lẩu", color: Color(0xFF4FC3F7), probability: 2),
    WheelOption(
        id: 8, label: "Nướng", color: Color(0xFF4DD0E1), probability: 2),
    WheelOption(
        id: 9, label: "Pizza", color: Color(0xFF4DB6AC), probability: 2),
    WheelOption(
        id: 10, label: "Hamburger", color: Color(0xFF81C784), probability: 2),
    WheelOption(
        id: 11, label: "Đồ Hàn", color: Color(0xFFAED581), probability: 2),
    WheelOption(
        id: 12, label: "Đồ Thái", color: Color(0xFFDCE775), probability: 2),
    WheelOption(
        id: 13, label: "Mì cay", color: Color(0xFFFFF176), probability: 2),
    WheelOption(id: 14, label: "Chè", color: Color(0xFFFFD54F), probability: 2),
    WheelOption(
        id: 15, label: "Đồ vặt", color: Color(0xFFFFB74D), probability: 2),
    WheelOption(
        id: 16, label: "Bánh tráng", color: Color(0xFFFF8A65), probability: 2),
    WheelOption(
        id: 17, label: "Cơm tấm", color: Color(0xFFA1887F), probability: 2),
    WheelOption(
        id: 18, label: "Cháo", color: Color(0xFFE0E0E0), probability: 2),
    WheelOption(
        id: 19, label: "Hải sản", color: Color(0xFF90A4AE), probability: 2),
    WheelOption(
        id: 20, label: "Gà rán", color: Color(0xFFF44336), probability: 2),
    WheelOption(
        id: 21, label: "Trà sữa", color: Color(0xFFE91E63), probability: 2),
    WheelOption(
        id: 22, label: "Cafe", color: Color(0xFF9C27B0), probability: 2),
    WheelOption(
        id: 23, label: "Sinh tố", color: Color(0xFF673AB7), probability: 2),
    WheelOption(
        id: 24, label: "Nước ép", color: Color(0xFF3F51B5), probability: 2),
    WheelOption(
        id: 25, label: "Soda", color: Color(0xFF2196F3), probability: 2),
    WheelOption(id: 26, label: "Bia", color: Color(0xFF03A9F4), probability: 2),
    WheelOption(
        id: 27, label: "Xem phim", color: Color(0xFF00BCD4), probability: 2),
    WheelOption(
        id: 28, label: "Chơi game", color: Color(0xFF009688), probability: 2),
    WheelOption(
        id: 29, label: "Karaoke", color: Color(0xFF4CAF50), probability: 2),
    WheelOption(
        id: 30, label: "Công viên", color: Color(0xFF8BC34A), probability: 2),
    WheelOption(
        id: 31, label: "Dạo phố", color: Color(0xFFCDDC39), probability: 2),
    WheelOption(
        id: 32, label: "Chợ đêm", color: Color(0xFFFFEB3B), probability: 2),
    WheelOption(
        id: 33,
        label: "Trung tâm TM",
        color: Color(0xFFFFC107),
        probability: 2),
    WheelOption(
        id: 34, label: "Bar/Pub", color: Color(0xFFFF9800), probability: 2),
    WheelOption(
        id: 35, label: "Du lịch", color: Color(0xFFFF5722), probability: 2),
    WheelOption(id: 36, label: "Kem", color: Color(0xFF795548), probability: 2),
    WheelOption(
        id: 37, label: "Bảo tàng", color: Color(0xFF9E9E9E), probability: 2),
    WheelOption(
        id: 38, label: "Nhà sách", color: Color(0xFF607D8B), probability: 2),
    WheelOption(
        id: 39, label: "Triển lãm", color: Color(0xFF6D4C41), probability: 2),
    WheelOption(
        id: 40, label: "Xem kịch", color: Color(0xFF5D4037), probability: 2),
    WheelOption(
        id: 41,
        label: "Ngắm hoàng hôn",
        color: Color(0xFF3E2723),
        probability: 2),
    WheelOption(
        id: 42, label: "Chụp hình", color: Color(0xFF1B5E20), probability: 2),
    WheelOption(id: 43, label: "Bơi", color: Color(0xFF004D40), probability: 2),
    WheelOption(
        id: 44, label: "Leo núi", color: Color(0xFF263238), probability: 2),
    WheelOption(
        id: 45, label: "Boardgame", color: Color(0xFF212121), probability: 2),
    WheelOption(id: 46, label: "Ngủ", color: Color(0xFFB0BEC5), probability: 2),
    WheelOption(
        id: 47, label: "Nấu ăn", color: Color(0xFFCFD8DC), probability: 2),
    WheelOption(
        id: 48, label: "Ăn khuya", color: Color(0xFFD7CCC8), probability: 2),
    WheelOption(
        id: 49, label: "Mua sắm", color: Color(0xFFBCAAA4), probability: 2),
    WheelOption(
        id: 50,
        label: "Đi dạo",
        color: Color(0xFFA1887F),
        probability: 2),
  ];

  late List<WheelOption> _normalizedOptions;

  // Define Cafe position angle (in radians) - Cafe is at position 4 in 0-indexed list
  // For 9 segments, each segment is 40 degrees (360/9), so Cafe is approximately at 120 degrees (3 * 40)
  final double pointerPositionRadians =
      3 * 2 * pi / 9; // Position for "Cafe" segment

  @override
  void initState() {
    super.initState();
    _normalizedOptions = _normalizeOptions(_wheelOptions);

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 5000),
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    );

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isSpinning = false;
          // Use the predetermined result instead of calculating from angle
          _showResultModal = true;
          // Show result dialog immediately after animation completes
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showResultDialog();
          });
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // Normalize probabilities to sum up to 100%
  List<WheelOption> _normalizeOptions(List<WheelOption> options) {
    final totalProbability = options.fold<double>(
      0,
          (sum, option) => sum + option.probability,
    );

    return options
        .map((option) => WheelOption(
      id: option.id,
      label: option.label,
      color: option.color,
      probability: (option.probability / totalProbability) * 100,
    ))
        .toList();
  }

  // Get a random segment based on probability
  WheelOption _getRandomSegment() {
    final random = Random().nextDouble() * 100;
    double cumulativeProbability = 0;

    for (final option in _normalizedOptions) {
      cumulativeProbability += option.probability;
      if (random <= cumulativeProbability) {
        return option;
      }
    }

    return _normalizedOptions.last;
  }

  // Start spinning the wheel
  void _startSpin() {
    if (_isSpinning) return;

    setState(() {
      _isSpinning = true;
      _showResultModal = false;
    });

    // Get random winning segment
    final winningSegment = _getRandomSegment();
    // Set the result immediately, but don't show it yet
    _result = winningSegment;

    // Calculate spin parameters
    final segmentSize = 360 / _normalizedOptions.length;
    final winningIndex = _normalizedOptions.indexWhere(
          (option) => option.id == winningSegment.id,
    );

    // Generate random spin duration between 4-8 seconds
    final spinDuration = 4000 + Random().nextInt(4000);
    _controller.duration = Duration(milliseconds: spinDuration);

    // Random offset within segment for natural look
    final randomOffset = Random().nextDouble() * segmentSize * 0.8;

    // Full rotations based on duration
    final fullRotations = (spinDuration / 1000).floor() * 3;

    // Calculate stop angle for winning segment
    // The pointer is now at the "Cafe" position (approximately 120 degrees or pi/3 * 2 radians)
    // Convert pointer position from radians to degrees
    final pointerPositionDegrees = pointerPositionRadians * 180 / pi;

    // Calculate stop angle to align winning segment with the new pointer position
    final segmentAngle = 360 / _normalizedOptions.length;
    final stopAngle = (360 -
        (winningIndex * segmentAngle + randomOffset + segmentAngle / 2) +
        pointerPositionDegrees) %
        360;

    // Total rotation
    _finalRotation = fullRotations * 360 + stopAngle;

    // Custom animation to rotate to exact position
    _animation = Tween<double>(
      begin: 0,
      end: _finalRotation / 360, // Normalize to 0-1 for animation
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _controller.reset();
    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final wheelSize = min(size.width * 0.8, size.height * 0.45);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AppImages.backgroundWheel),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(size),
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildWheel(wheelSize, size),
                      SizedBox(height: _getResponsiveValue(size.width, 30)),
                      _buildSpinButton(size),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(Size size) {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Column(
            children: [
              Text(
                'LUCKY WHEEL',
                style: TextStyle(
                  fontSize: _getResponsiveValue(size.width, 24),
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontStyle: FontStyle.italic,
                ),
              ),
              Text(
                'FUNPLAY',
                style: TextStyle(
                  fontSize: _getResponsiveValue(size.width, 22),
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFFF6B6B),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWheel(double wheelSize, Size size) {
    return Container(
      width: wheelSize + _getResponsiveValue(size.width, 20),
      height: wheelSize + _getResponsiveValue(size.width, 50),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Outer wheel frame
          Container(
            width: wheelSize + _getResponsiveValue(size.width, 20),
            height: wheelSize + _getResponsiveValue(size.width, 20),
            decoration: BoxDecoration(
              color: const Color(0xFFFF4500),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
          ),

          // Rotating wheel
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _animation.value * 2 * pi,
                child: Container(
                  width: wheelSize,
                  height: wheelSize,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: CustomPaint(
                    painter: WheelPainter(
                      options: _normalizedOptions,
                      wheelSize: wheelSize,
                    ),
                  ),
                ),
              );
            },
          ),

          // Center pin
          Container(
            width: _getResponsiveValue(size.width, 30),
            height: _getResponsiveValue(size.width, 30),
            decoration: BoxDecoration(
              color: const Color(0xFFD4AF37),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Container(
                width: wheelSize * 0.05,
                height: wheelSize * 0.05,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),

          // Indicator at the "Cafe" position - MOVED FROM TOP
          Positioned(
            // Use trigonometric functions to position the pointer
            // For a circle, x = center.x + radius * cos(angle), y = center.y + radius * sin(angle)
            left: (size.width - wheelSize) / 2 +
                (wheelSize / 2) +
                cos(pointerPositionRadians) *
                    (wheelSize / 2 + _getResponsiveValue(size.width, 10)),
            top: (size.height - wheelSize) / 4 +
                (wheelSize / 2) +
                sin(pointerPositionRadians) *
                    (wheelSize / 2 + _getResponsiveValue(size.width, 10)),
            child: Transform.rotate(
              angle: pointerPositionRadians + pi,
              // Rotate the triangle to point inward
              child: Container(
                width: _getResponsiveValue(size.width, 30),
                height: _getResponsiveValue(size.width, 30),
                child: CustomPaint(
                  painter: InvertedTrianglePointerPainter(),
                ),
              ),
            ),
          ),

          // Wheel stand
          Positioned(
            bottom: 0,
            child: Container(
              width: _getResponsiveValue(size.width, 120),
              height: _getResponsiveValue(size.width, 40),
              decoration: BoxDecoration(
                color: const Color(0xFFFF4500),
                borderRadius: BorderRadius.only(
                  bottomLeft:
                  Radius.circular(_getResponsiveValue(size.width, 20)),
                  bottomRight:
                  Radius.circular(_getResponsiveValue(size.width, 20)),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.25),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpinButton(Size size) {
    return Container(
      margin: EdgeInsets.only(
        top: _getResponsiveValue(size.width, 20),
      ),
      child: ElevatedButton(
        onPressed: _isSpinning ? null : _startSpin,
        style: ElevatedButton.styleFrom(
          backgroundColor:
          _isSpinning ? const Color(0xFFFF8C69) : const Color(0xFFFF4500),
          padding: EdgeInsets.symmetric(
            vertical: _getResponsiveValue(size.width, 12),
            horizontal: _getResponsiveValue(size.width, 40),
          ),
          shape: RoundedRectangleBorder(
            borderRadius:
            BorderRadius.circular(_getResponsiveValue(size.width, 25)),
          ),
          elevation: 5,
        ),
        child: Text(
          _isSpinning ? 'SPINNING...' : 'SPIN',
          style: TextStyle(
            color: Colors.white,
            fontSize: _getResponsiveValue(size.width, 18),
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  // Calculate responsive dimensions
  double _getResponsiveValue(double screenWidth, double baseValue) {
    const baseWidth = 375.0; // Base width for calculations
    return (screenWidth / baseWidth) * baseValue;
  }

  // Modified result dialog with "Tìm kiếm" button
  void _showResultDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          'Kết quả',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color(0xFFFF4500),
          ),
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _result!.label,
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: _result!.color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            const Text(
              'Xin chúc mừng! Bạn đã quay được kết quả thú vị.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          // Row container for buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Close button
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {
                    _showResultModal = false;
                  });
                },
                child: const Text(
                  'Đóng',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // Search button
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF4500),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {
                    _showResultModal = false;
                  });

                  // Navigate to SearchScreen with the result as initialQuery
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => SearchScreen(
                        initialQuery: _result!.label,
                      ),
                    ),
                  );
                },
                child: const Text(
                  'Tìm kiếm',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Inverted triangle pointer painter (points up instead of down)
class InvertedTrianglePointerPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.width / 2, size.height); // Point at bottom
    path.lineTo(0, 0); // Top left
    path.lineTo(size.width, 0); // Top right
    path.close();

    canvas.drawPath(path, paint);

    // Add shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    canvas.drawPath(path, shadowPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Custom painter for wheel segments and labels
class WheelPainter extends CustomPainter {
  final List<WheelOption> options;
  final double wheelSize;

  WheelPainter({required this.options, required this.wheelSize});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final segmentAngle = 2 * pi / options.length;

    // Add border between segments
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    canvas.drawCircle(center, radius, borderPaint);

    for (int i = 0; i < options.length; i++) {
      final startAngle = i * segmentAngle;
      final endAngle = (i + 1) * segmentAngle;
      final option = options[i];

      // Draw segment
      final paint = Paint()
        ..color = option.color
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        segmentAngle,
        true,
        paint,
      );

      // Draw segment border
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        segmentAngle,
        true,
        borderPaint..style = PaintingStyle.stroke,
      );

      // Draw text
      final midAngle = startAngle + segmentAngle / 2;
      final textRadius = radius * 0.7; // Position text a bit further out
      final textPoint = Offset(
        center.dx + textRadius * cos(midAngle),
        center.dy + textRadius * sin(midAngle),
      );

      // Set up text painter
      final textStyle = TextStyle(
        color: option.color == Colors.white ? Colors.black : Colors.white,
        fontSize: max(radius * 0.085, 12), // Slightly larger text
        fontWeight: FontWeight.bold,
      );

      final textPainter = TextPainter(
        text: TextSpan(text: option.label, style: textStyle),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();

      // Rotate and position text
      canvas.save();
      canvas.translate(textPoint.dx, textPoint.dy);

      // Adjust text rotation to make it readable
      double textRotation = midAngle;
      if (midAngle > pi / 2 && midAngle < 3 * pi / 2) {
        textRotation += pi;
      }
      canvas.rotate(textRotation);

      // Center the text
      textPainter.paint(
        canvas,
        Offset(-textPainter.width / 2, -textPainter.height / 2),
      );

      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}