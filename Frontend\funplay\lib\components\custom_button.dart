import 'package:flutter/material.dart';
import 'package:funplay/constants/colors.dart';

import '../constants/colors.dart';

enum ButtonVariant { primary, outline, google }

class CustomButton extends StatelessWidget {
  final String title;
  final ButtonVariant variant;
  final VoidCallback onPress;
  final String? iconPath;
  final EdgeInsets? margin;

  const CustomButton({
    Key? key,
    required this.title,
    required this.variant,
    required this.onPress,
    this.iconPath,
    this.margin,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: ElevatedButton(
        onPressed: onPress,
        style: ElevatedButton.styleFrom(
          backgroundColor: _getBackgroundColor(),
          foregroundColor: _getForegroundColor(),
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: _getBorderSide(),
          ),
          elevation: variant == ButtonVariant.google ? 2 : 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (iconPath != null) ...[
              Image.asset(
                iconPath!,
                width: 24,
                height: 24,
              ),
              const SizedBox(width: 8),
            ],
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: _getForegroundColor(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (variant) {
      case ButtonVariant.primary:
        return AppColors.primary;
      case ButtonVariant.outline:
        return Colors.transparent;
      case ButtonVariant.google:
        return AppColors.white;
    }
  }

  Color _getForegroundColor() {
    switch (variant) {
      case ButtonVariant.primary:
        return AppColors.white;
      case ButtonVariant.outline:
        return AppColors.primary;
      case ButtonVariant.google:
        return AppColors.black;
    }
  }

  BorderSide _getBorderSide() {
    switch (variant) {
      case ButtonVariant.primary:
        return BorderSide.none;
      case ButtonVariant.outline:
        return const BorderSide(color: AppColors.primary, width: 1);
      case ButtonVariant.google:
        return const BorderSide(color: Gray.light, width: 1);
    }
  }
}