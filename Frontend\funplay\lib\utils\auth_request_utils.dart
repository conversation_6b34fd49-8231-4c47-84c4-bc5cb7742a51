// utils/auth_request_utils.dart

import 'dart:convert';
import 'auth_utils.dart';
import 'api_utils.dart';
import '../api/config.dart';

class AuthRequestUtils {
  static Future<Map<String, String>> createAuthHeaders([Map<String, String>? additionalHeaders]) async {
    final token = await AuthUtils.getAuthToken();

    final Map<String, String> headers = {
      'Content-Type': 'application/json',
      ...?additionalHeaders,
    };

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  static Future<Map<String, dynamic>> authRequest(
      String endpoint, {
        String method = 'GET',
        Map<String, String>? headers,
        dynamic body,
      }) async {
    try {
      final Map<String, String> authHeaders = await createAuthHeaders(headers);

      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiBaseUrl}$endpoint',
        method: method,
        headers: authHeaders,
        body: body,
      );

      return ApiUtils.handleApiResponse(response);
    } catch (error) {
      // Handle token expiration
      if (error is Exception && error.toString().contains('401')) {
        print('Auth token expired or invalid');
      }
      throw error;
    }
  }

  static Future<Map<String, dynamic>> authRequestGetRecommend(
      String endpoint, {
        String method = 'GET',
        Map<String, String>? headers,
        dynamic body,
      }) async {
    try {
      final Map<String, String> authHeaders = await createAuthHeaders(headers);

      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiRecommend}$endpoint',
        method: method,
        headers: authHeaders,
        body: body,
      );

      return ApiUtils.handleApiResponse(response);
    } catch (error) {
      // Handle token expiration
      if (error is Exception && error.toString().contains('401')) {
        print('Auth token expired or invalid');
      }
      throw error;
    }
  }

  static final authFetch = _AuthFetch();
}

class _AuthFetch {
  Future<Map<String, dynamic>> get(String endpoint, {Map<String, String>? headers}) {
    return AuthRequestUtils.authRequest(
      endpoint,
      method: 'GET',
      headers: headers,
    );
  }

  Future<Map<String, dynamic>> getRecommend(String endpoint, {Map<String, String>? headers}) {
    return AuthRequestUtils.authRequestGetRecommend(
      endpoint,
      method: 'GET',
      headers: headers,
    );
  }

  Future<Map<String, dynamic>> post(String endpoint, Map<String, dynamic> data, {Map<String, String>? headers}) {
    return AuthRequestUtils.authRequest(
      endpoint,
      method: 'POST',
      headers: headers,
      body: jsonEncode(data),
    );
  }

  Future<Map<String, dynamic>> put(String endpoint, Map<String, dynamic> data, {Map<String, String>? headers}) {
    return AuthRequestUtils.authRequest(
      endpoint,
      method: 'PUT',
      headers: headers,
      body: jsonEncode(data),
    );
  }

  Future<Map<String, dynamic>> patch(String endpoint, Map<String, dynamic> data, {Map<String, String>? headers}) {
    return AuthRequestUtils.authRequest(
      endpoint,
      method: 'PATCH',
      headers: headers,
      body: jsonEncode(data),
    );
  }

  Future<Map<String, dynamic>> delete(String endpoint, {Map<String, String>? headers}) {
    return AuthRequestUtils.authRequest(
      endpoint,
      method: 'DELETE',
      headers: headers,
    );
  }
}
