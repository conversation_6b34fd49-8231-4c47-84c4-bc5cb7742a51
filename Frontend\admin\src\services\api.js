import axios from 'axios';

// Base URLs for different services
const CRAWLER_API = 'http://localhost:5000/api/v1';
const PREPROCESSOR_API = 'http://localhost:5005/api/v1';
const TAGGER_API = 'http://localhost:5010/api/v1';
const POSITION_API = 'http://localhost:5015/api/v1';
const POST_API = 'http://localhost:8085/api/post';

export const startCrawling = (cookie) => axios.get(`${CRAWLER_API}/scrape`, { params: { cookie } });
export const stopCrawling = () => axios.post(`${CRAWLER_API}/stop-crawl`);
export const updateData = (cookie) => axios.put(`${CRAWLER_API}/update`, null, { params: { cookie } });

export const preprocessData = () => axios.post(`${PREPROCESSOR_API}/preprocess`);
export const summarizeData = () => axios.post(`${PREPROCESSOR_API}/summarize`);

export const tagData = () => axios.post(`${TAGGER_API}/tag-data`);

export const tagPosition = () => axios.get(`${POSITION_API}/tag-position`);

export const getAllPosts = () => axios.get(`${POST_API}/getAll`);

export const getPagedPosts = (limit = 10, page = 1, searchTerm = '') => {
    try {
        let url = `${POST_API}/getAll?limit=${limit}&page=${page}`;

        if (searchTerm) {
            url += `&search=${encodeURIComponent(searchTerm)}`;
        }

        return axios.get(url);
    } catch (error) {
        console.log(error);
    }
};