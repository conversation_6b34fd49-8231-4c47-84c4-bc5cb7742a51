import 'dart:convert';
import '../utils/api_utils.dart';
import 'config.dart';

class SearchApi {
  static Future<Map<String, dynamic>> search(String query) async {
    try {
      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiBaseUrl}/getData/search',
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        queryParams: {
          'search': query,
        },
      );

      return ApiUtils.handleApiResponse(response);
    } catch (error) {
      print('Search error: $error');
      throw error;
    }
  }
}