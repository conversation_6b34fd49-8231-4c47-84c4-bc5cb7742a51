import pandas as pd

class HybridRecommender:
    """Handles hybrid recommendations that combine content-based and collaborative filtering approaches"""
    
    def __init__(self, content_recommender, collaborative_recommender):
        """
        Initialize hybrid recommender
        
        Parameters:
        -----------
        content_recommender : ContentRecommender
            Content-based recommender instance
        collaborative_recommender : CollaborativeRecommender
            Collaborative filtering recommender instance
        """
        self.content_recommender = content_recommender
        self.collaborative_recommender = collaborative_recommender
        
        # Weights for each recommendation approach
        self.content_weight = 0.5  # Equal priority
        self.collaborative_weight = 0.5  # Equal priority
    
    def _normalize_scores(self, df, score_col):
        """
        Normalize scores to be between 0 and 1
        
        Parameters:
        -----------
        df : pandas DataFrame
            DataFrame containing scores to normalize
        score_col : str
            Column name of the score to normalize
            
        Returns:
        --------
        df : pandas DataFrame
            DataFrame with normalized scores
        """
        if df.empty:
            return df
            
        max_score = df[score_col].max()
        min_score = df[score_col].min()
        
        if max_score == min_score:
            df['normalized_score'] = 1.0
        else:
            df['normalized_score'] = (df[score_col] - min_score) / (max_score - min_score)
            
        return df
    
    def get_recommendations(self, user_id):

        collaborative = self.collaborative_recommender.get_recommendations(user_id)

        content_based = self.content_recommender.get_recommendations(user_id)
        print("HybridRecommender: Content-based recommendations:", content_based)
        

        if content_based.empty and collaborative.empty:
            return pd.DataFrame(columns=['post_id', 'final_score'])
        
        # Normalize the scores from each approach
        if not content_based.empty:
            content_based = self._normalize_scores(content_based, 'similarity_score')
        if not collaborative.empty:
            collaborative = self._normalize_scores(collaborative, 'predicted_score')
        
        # Combine all recommendations
        all_recommendations = {}
        
        if not content_based.empty:
            for _, row in content_based.iterrows():
                post_id = row['post_id']
                all_recommendations[post_id] = {
                    'post_id': post_id,
                    'content_score': float(row['normalized_score'] * self.content_weight),
                    'collaborative_score': 0.0  # Default value
                }
        
        if not collaborative.empty:
            for _, row in collaborative.iterrows():
                post_id = row['post_id']
                collab_score = float(row['normalized_score'] * self.collaborative_weight)
                if post_id in all_recommendations:
                    all_recommendations[post_id]['collaborative_score'] = collab_score
                else:
                    all_recommendations[post_id] = {
                        'post_id': post_id,
                        'content_score': 0.0,  # Default value
                        'collaborative_score': collab_score
                    }
        
        # Calculate final score for each recommendation
        recommendations_list = []
        for item in all_recommendations.values():
            item['final_score'] = item['content_score'] + item['collaborative_score']
            recommendations_list.append(item)
        
        # Convert to DataFrame and sort by final score
        result = pd.DataFrame(recommendations_list)
        if result.empty:
            return pd.DataFrame(columns=['post_id', 'final_score'])
            
        return result.sort_values('final_score', ascending=False)[['post_id', 'final_score']]