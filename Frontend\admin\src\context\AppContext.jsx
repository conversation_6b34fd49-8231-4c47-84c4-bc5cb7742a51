import React, { createContext, useState, useContext, useEffect } from 'react';

// Create context
const AppContext = createContext();

// Custom hook for using the context
export const useAppContext = () => useContext(AppContext);

export const AppProvider = ({ children }) => {
  // Crawler state
  const [isCrawling, setIsCrawling] = useState(() => {
    return JSON.parse(localStorage.getItem('isCrawling')) || false;
  });
  const [crawlStatus, setCrawlStatus] = useState(() => {
    return localStorage.getItem('crawlStatus') || 'Sẵn sàng để crawl';
  });
  const [crawlError, setCrawlError] = useState(() => {
    return localStorage.getItem('crawlError') || null;
  });
  const [lastCrawlTime, setLastCrawlTime] = useState(() => {
    return localStorage.getItem('lastCrawlTime') || null;
  });

  // Updater state
  const [isUpdating, setIsUpdating] = useState(() => {
    return JSON.parse(localStorage.getItem('isUpdating')) || false;
  });
  const [updateProgress, setUpdateProgress] = useState(() => {
    return JSON.parse(localStorage.getItem('updateProgress')) || 0;
  });
  const [updateStatus, setUpdateStatus] = useState(() => {
    return localStorage.getItem('updateStatus') || 'Sẵn sàng để cập nhật';
  });
  const [updateError, setUpdateError] = useState(() => {
    return localStorage.getItem('updateError') || null;
  });
  const [updateSteps, setUpdateSteps] = useState(() => {
    const savedSteps = localStorage.getItem('updateSteps');
    if (savedSteps) {
      return JSON.parse(savedSteps);
    } else {
      return [
        { id: 1, name: "Cập nhật dữ liệu", status: "pending", message: "" },
        { id: 2, name: "Tiền xử lý", status: "pending", message: "" },
        { id: 3, name: "Tóm tắt", status: "pending", message: "" },
        { id: 4, name: "Gắn thẻ dữ liệu", status: "pending", message: "" },
        { id: 5, name: "Gắn vị trí thẻ", status: "pending", message: "" },
      ];
    }
  });

  // Posts state - không lưu vào localStorage nữa
  const [posts, setPosts] = useState([]);

  // Update localStorage whenever state changes
  useEffect(() => {
    localStorage.setItem('isCrawling', JSON.stringify(isCrawling));
    localStorage.setItem('crawlStatus', crawlStatus);
    if (crawlError) {
      localStorage.setItem('crawlError', crawlError);
    } else {
      localStorage.removeItem('crawlError');
    }
    if (lastCrawlTime) {
      localStorage.setItem('lastCrawlTime', lastCrawlTime);
    }
  }, [isCrawling, crawlStatus, crawlError, lastCrawlTime]);

  useEffect(() => {
    localStorage.setItem('isUpdating', JSON.stringify(isUpdating));
    localStorage.setItem('updateProgress', JSON.stringify(updateProgress));
    localStorage.setItem('updateStatus', updateStatus);
    localStorage.setItem('updateSteps', JSON.stringify(updateSteps));
    if (updateError) {
      localStorage.setItem('updateError', updateError);
    } else {
      localStorage.removeItem('updateError');
    }
  }, [isUpdating, updateProgress, updateStatus, updateSteps, updateError]);

  // Reset crawler function
  const resetCrawlProcess = () => {
    setIsCrawling(false);
    setCrawlStatus('Sẵn sàng để crawl');
    setCrawlError(null);
    
    localStorage.removeItem('isCrawling');
    localStorage.removeItem('crawlStatus');
    localStorage.removeItem('crawlError');
  };

  // Reset updater function
  const resetUpdateProcess = () => {
    setUpdateProgress(0);
    setUpdateStatus('Sẵn sàng để cập nhật');
    setUpdateSteps((steps) =>
      steps.map((step) => ({
        ...step,
        status: "pending",
        message: "",
      }))
    );
    setUpdateError(null);
    
    localStorage.removeItem('isUpdating');
    localStorage.removeItem('updateProgress');
    localStorage.removeItem('updateStatus');
    localStorage.setItem('updateSteps', JSON.stringify([
      { id: 1, name: "Cập nhật dữ liệu", status: "pending", message: "" },
      { id: 2, name: "Tiền xử lý", status: "pending", message: "" },
      { id: 3, name: "Tóm tắt", status: "pending", message: "" },
      { id: 4, name: "Gắn thẻ dữ liệu", status: "pending", message: "" },
      { id: 5, name: "Gắn vị trí thẻ", status: "pending", message: "" },
    ]));
    localStorage.removeItem('updateError');
  };

  // Helper for update steps
  const updateStepStatus = (stepId, newStatus, message = "") => {
    setUpdateSteps((steps) =>
      steps.map((step) =>
        step.id === stepId ? { ...step, status: newStatus, message } : step
      )
    );
  };

  // Helper for calculating progress
  const calculateProgress = (steps) => {
    const completedSteps = steps.filter(
      (step) => step.status === "completed"
    ).length;
    return Math.round((completedSteps / steps.length) * 100);
  };

  const value = {
    // Crawler state
    isCrawling, setIsCrawling,
    crawlStatus, setCrawlStatus,
    crawlError, setCrawlError,
    lastCrawlTime, setLastCrawlTime,
    resetCrawlProcess,
    
    // Updater state
    isUpdating, setIsUpdating,
    updateProgress, setUpdateProgress,
    updateStatus, setUpdateStatus,
    updateError, setUpdateError,
    updateSteps, setUpdateSteps,
    updateStepStatus,
    calculateProgress,
    resetUpdateProcess,
    
    // Posts state
    posts, setPosts
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};