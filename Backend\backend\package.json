{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index.js", "dev": "concurrently \"cd ../module_crawl_data && python main.py\" \"cd ../module_preprocess_data && python main.py\" \"cd ../module_recommendation && python main.py\" \"nodemon index.js\""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@grpc/grpc-js": "^1.12.5", "@grpc/proto-loader": "^0.7.13", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.5", "mysql2": "^3.12.0"}, "devDependencies": {"concurrently": "^9.1.2", "nodemon": "^3.1.9"}}