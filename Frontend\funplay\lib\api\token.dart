import 'dart:convert';
import '../utils/api_utils.dart';
import '../utils/auth_utils.dart';
import 'config.dart';

class TokenApi {
  static Future<Map<String, dynamic>> refreshToken(String userId, String oldRefreshToken) async {
    try {
      final response = await ApiUtils.fetchWithTimeout(
        '${ApiConfig.apiBaseUrl}/auth/refresh-token',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'userId': userId,
          'oldRefreshToken': oldRefreshToken,
        }),
      );

      return ApiUtils.handleApiResponse(response);
    } catch (error) {
      print('Refresh token error: $error');
      throw error;
    }
  }
}