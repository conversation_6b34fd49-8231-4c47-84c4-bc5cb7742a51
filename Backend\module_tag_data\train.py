import time
import math
import arg<PERSON><PERSON>

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.data import WeightedRandomSampler
import utils

def train(model, dataloader, optimizer, device, criterion):
    """
    @param model (BalancedRNN)
    @param dataloader (DataLoader)
    @param optimizer (torch.optim)
    @param device (torch.device)
    @param pos_weight (float): weight for positive class
    @return epoch_loss (float): model's loss of this epoch
    @return epoch_acc (float): model's accuracy of this epoch 
    """
    epoch_loss = 0
    epoch_acc = 0
    
    model.train()
    
    for batch in dataloader:
        optimizer.zero_grad()
        reviews, reviews_lengths = batch["reviews"]
        reviews = reviews.to(device)
        predictions = model(reviews, reviews_lengths).squeeze(1)
        sentiments = batch["sentiments"].to(device)
        
        # Calculate weighted loss
        loss = criterion(predictions, sentiments)
        
        # Calculate balanced accuracy
        predictions_binary = torch.sigmoid(predictions) >= 0.5
        acc = balanced_binary_accuracy(predictions_binary, sentiments)
        
        loss.backward()
        # Gradient clipping to prevent exploding gradients
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        epoch_loss += loss.item()
        epoch_acc += acc.item()
    
    batch_num = len(dataloader)
    return epoch_loss / batch_num, epoch_acc / batch_num

def balanced_binary_accuracy(predictions, targets):
    """
    Calculate balanced accuracy for binary classification
    """
    positive_acc = ((predictions == 1) & (targets == 1)).float().sum() / (targets == 1).float().sum()
    negative_acc = ((predictions == 0) & (targets == 0)).float().sum() / (targets == 0).float().sum()
    return (positive_acc + negative_acc) / 2

def balance_data_loader(train_dataset, dataset, batch_size):
    # Extract labels, ensuring they're converted to binary integers
    labels = [1 if dataset[i][1] == 'positive' else 0 for i in range(len(train_dataset))]
    
    # Convert to tensor with integer type
    labels_tensor = torch.tensor(labels, dtype=torch.long)
    
    # Calculate number of samples for each class
    class_counts = torch.bincount(labels_tensor)
    
    # Calculate class weights
    total_samples = len(labels)
    max_class_samples = max(class_counts)
    
    # Compute weights to ensure equal representation
    class_weights = torch.tensor([
        max_class_samples / count if count > 0 else 0 
        for count in class_counts
    ], dtype=torch.float)
    
    # Assign weights to each sample
    sample_weights = [class_weights[label] for label in labels]
    
    # Create sampler with replacement
    sampler = WeightedRandomSampler(sample_weights, 
                                     num_samples=total_samples, 
                                     replacement=True)
    
    # Create DataLoader with sampler
    dataloader = DataLoader(train_dataset, 
                            batch_size=batch_size, 
                            sampler=sampler, 
                            collate_fn=dataset.collate_fn)
    return dataloader

def evaluate(model, dataloader, criterion, device):
    """
    @param model (RNN)
    @param dataloader (DataLoader)
    @param criterion (torch.nn.modules.loss)
    @param device (torch.device)
    @return epoch_loss (float): model's loss of this epoch
    @return epoch_acc (float): model's accuracy of this epoch 
    """
    epoch_loss = 0
    epoch_acc = 0
    
    model.eval()
    
    with torch.no_grad():
        for batch in dataloader:
            reviews, reviews_lengths = batch["reviews"]
            reviews = reviews.to(device)
            predictions = model(reviews, reviews_lengths).squeeze(1)
          
            sentiments = batch["sentiments"].to(device)
            loss = criterion(predictions, sentiments)  
            acc = utils.binary_accuracy(predictions, sentiments)

            epoch_loss += loss.item()
            epoch_acc += acc.item()
        
    batch_num = len(dataloader)
    return epoch_loss / batch_num, epoch_acc / batch_num

def main(config_fpath):
    config = utils.get_config(config_fpath)

    print("Create logs folder...")
    current_log_dir, state_dir = utils.create_logs_dir(config)
    print(f"The current log dir is {current_log_dir}")

    print("Creating vocabulary...")     
    vocab, word_embedding = utils.get_vocab_and_word2vec(config, 
                                                         current_log_dir)    
    pad_idx = vocab["<pad>"]

    print("Loading dataset...")
    dataset, (train_dataset, valid_dataset, test_dataset) = utils.get_dataset(config,
                                                                   vocab,
                                                                   current_log_dir)

    print("Creating dataloader...")
    batch_size = config["train"]["batch_size"]
    train_dataloader = balance_data_loader(train_dataset, dataset, batch_size=batch_size)
    
    valid_dataloader = DataLoader(valid_dataset,
                                batch_size=batch_size,
                                shuffle=True,
                                collate_fn=dataset.collate_fn)
    test_dataloader = DataLoader(test_dataset,
                                batch_size=batch_size,
                                shuffle=False,
                                collate_fn=dataset.collate_fn)
    
    print("Creating model...")
    model = utils.get_model(config, vocab, word_embedding)

    print("Creating optimizer and loss function...")
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    optimizer = optim.Adam(model.parameters())
    model = model.to(device)

    print("Creating SummaryWriter...")
    writer = utils.get_writer(log_dir=current_log_dir)

    print("Training...")
    n_epochs = config["train"]["n_epochs"]
    save_epoch = config["train"]["save_epoch"]

    state_fpath = config["continue"]["state_fpath"]

    # Early stopping parameters
    patience = 3  # số epoch chờ đợi không cải thiện performance
    min_delta = 0.001  # ngưỡng tối thiểu để coi là cải thiện
    best_valid_loss = float("inf")
    early_stopping_counter = 0
    best_model_state = None

    if state_fpath is not None:
        checkpoint = torch.load(state_fpath)
        model.load_state_dict(checkpoint["model_state_dict"])
        optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        begin_epoch = checkpoint["epoch"] + 1
        print(f"Continue after epoch {begin_epoch}")
    else:
        begin_epoch = 0

    print("Calculating class weights...")
    pos_weight = utils.calculate_pos_weight(train_dataset)
    print(f"Positive class weight: {pos_weight:.4f}")
    
    criterion = nn.BCEWithLogitsLoss(pos_weight=torch.tensor([pos_weight])).to(device)
    
    for epoch in range(begin_epoch, n_epochs):
        start_time = time.time()
        train_loss, train_acc = train(model, train_dataloader, optimizer, device, criterion)
        valid_loss, valid_acc = evaluate(model, valid_dataloader, criterion, device)
        end_time = time.time()

        epoch_mins, epoch_secs = utils.epoch_time(start_time, end_time)
        
        # Log metrics to tensorboard
        loss_dict = {"valid": valid_loss, "train": train_loss}
        accuracy_dict = {"valid": valid_acc, "train": train_acc}
        writer.add_scalars(main_tag="loss",
                           tag_scalar_dict=loss_dict,
                           global_step=epoch)
        writer.add_scalars(main_tag="accuracy",
                           tag_scalar_dict= accuracy_dict,
                           global_step=epoch)
        
        print(f"Epoch: {epoch+1:02} | Epoch Time: {epoch_mins}m {epoch_secs}s")
        print(f"\tTrain Loss: {train_loss:.3f} | Train Acc: {train_acc*100:.2f}%")
        print(f"\t Val. Loss: {valid_loss:.3f} |  Val. Acc: {valid_acc*100:.2f}%")

        # Early stopping logic
        if valid_loss < best_valid_loss - min_delta:
            best_valid_loss = valid_loss
            early_stopping_counter = 0
            # Lưu lại trạng thái model tốt nhất
            best_model_state = {
                "model_state_dict": model.state_dict(),
                "optimizer_state_dict": optimizer.state_dict(),
                "epoch": epoch
            }
            # Lưu model tốt nhất
            torch.save(model, f"{current_log_dir}/best_model.pt")
        else:
            early_stopping_counter += 1
            
        # Kiểm tra điều kiện dừng sớm
        if early_stopping_counter >= patience:
            print(f"Early stopping triggered after {epoch+1} epochs!")
            break

        if ((epoch + 1) % save_epoch) == 0:
            print(f"Saving state at epoch {epoch+1}")
            state_fpath = f"{state_dir}/epoch{epoch+1}.pt"
            torch.save({"epoch": epoch,
                        "model_state_dict": model.state_dict(),
                        "optimizer_state_dict": optimizer.state_dict()},
                       state_fpath)

    # Nếu đã dừng sớm, nạp lại model tốt nhất
    if best_model_state is not None:
        model.load_state_dict(best_model_state["model_state_dict"])

    print("Testing...")
    test_loss, test_acc = evaluate(model, test_dataloader, criterion, device)

    print(f"Test Loss: {test_loss:.3f} | Test Acc: {test_acc*100:.2f}%")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train Vietnamese Sentiment Analyis model")

    parser.add_argument("--config", 
                        default="configs/config.yml", 
                        help="path to config file",
                        dest="config_fpath")
    
    args = parser.parse_args()

    main(**vars(args))