import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class AuthUtils {
  static const String AUTH_TOKEN_KEY = 'auth_token';
  static const String REFRESH_TOKEN_KEY = 'refresh_token';
  static const String USER_DATA_KEY = 'user_data';

  // Store authentication data
  static Future<void> storeAuthData(
      String token, String refreshToken, Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AUTH_TOKEN_KEY, token);
    await prefs.setString(REFRESH_TOKEN_KEY, refreshToken);
    await prefs.setString(USER_DATA_KEY, jsonEncode(userData));
  }

  // Update auth token
  static Future<void> updateAuthToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AUTH_TOKEN_KEY, token);
  }

  // Update refresh token
  static Future<void> updateRefreshToken(String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(REFRESH_TOKEN_KEY, refreshToken);
  }

  // Update user data
  static Future<void> updateUserData(Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(USER_DATA_KEY, jsonEncode(userData));
  }

  // Get auth token
  static Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AUTH_TOKEN_KEY);
  }

  // Get refresh token
  static Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(REFRESH_TOKEN_KEY);
  }

  // Get user data
  static Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(USER_DATA_KEY);
    if (userDataString != null) {
      return jsonDecode(userDataString) as Map<String, dynamic>;
    }
    return null;
  }

  // Clear auth data
  static Future<void> clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AUTH_TOKEN_KEY);
    await prefs.remove(REFRESH_TOKEN_KEY);
    await prefs.remove(USER_DATA_KEY);
  }

  static bool validateEmail(String email) {
    final RegExp regex = RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$');
    return regex.hasMatch(email.toLowerCase());
  }

  static bool validatePassword(String password) {
    // Minimum 8 characters, at least one letter and one number
    final RegExp regex = RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$');
    return regex.hasMatch(password);
  }

  static bool validateDateOfBirth(String dob) {
    // Basic format validation
    final RegExp regex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
    if (!regex.hasMatch(dob)) return false;

    // Check if it's a valid date
    final parts = dob.split('/');
    final day = int.parse(parts[0]);
    final month = int.parse(parts[1]) - 1;
    final year = int.parse(parts[2]);

    final dateObj = DateTime(year, month, day);
    return dateObj.day == day && dateObj.month == month && dateObj.year == year;
  }
}
