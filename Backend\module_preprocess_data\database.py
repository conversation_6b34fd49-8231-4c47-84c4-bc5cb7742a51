import mysql.connector
from datetime import datetime, timedelta

class DatabaseManager:
    def __init__(self, mysql_config):
        self.mysql_config = mysql_config
        self.connection = self.create_mysql_connection()
        
    def create_mysql_connection(self):
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            print("Connection successful")
            return connection
        except mysql.connector.Error as err:
            print(f"Error connecting to MySQL: {err}")
            return None
            
    def execute_transaction(self, queries, params_list=None):
        if not self.connection:
            return False, "No database connection"
        
        connection = None
        cursor = None
        try:
            # Open a new connection for this transaction
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor()
            
            results = []
            for i, query in enumerate(queries):
                # Get corresponding parameters if they exist
                params = params_list[i] if params_list and i < len(params_list) else None
                cursor.execute(query, params)
                results.append(cursor.rowcount)
            
            connection.commit()
            return True, results
            
        except mysql.connector.Error as err:
            if connection and connection.is_connected():
                connection.rollback()
            return False, f"Error executing transaction: {err}"
            
        finally:
            if cursor:
                cursor.close()
            if connection and connection.is_connected():
                connection.close()

        
    def execute_select(self, query, params=None):
        """Execute a SELECT query and return the results."""
        if not self.connection:
            return False, "No database connection"
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, params or ())
            results = cursor.fetchall()  # Fetch all results
            cursor.close()  # Close the cursor
            return True, results
        except mysql.connector.Error as err:
            return False, f"Error executing SELECT query: {err}"