from database import DatabaseManager
import csv
import os
from difflib import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import List, <PERSON><PERSON>, Dict
from unidecode import unidecode
import re
import unicodedata

class PreprocessManager:
    def __init__(self, db_manager):
        self.db_manager = db_manager
        # Define fancy Unicode character ranges that need normalization
        self.fancy_fonts = {
            # Mathematical alphanumeric symbols (bold, italic, etc.)
            'mathematical': [
                (0x1D400, 0x1D7FF),  # Mathematical Alphanumeric Symbols
            ],
            # Fullwidth characters often used in social media
            'fullwidth': [
                (0xFF00, 0xFFEF),  # Fullwidth Forms
            ],
            # Special symbols that might be used decoratively
            'symbols': [
                (0x2700, 0x27BF),  # Dingbats
                (0x1F300, 0x1F5FF),  # Miscellaneous Symbols And Pictographs
            ],
        }

    def delete_messages(self):
        try:
            # Read words from word_posts.txt for posts
            word_file_path_posts = "Data/word_posts.txt"
            if not os.path.exists(word_file_path_posts):
                return False, f"File {word_file_path_posts} does not exist"

            with open(word_file_path_posts, "r", encoding="utf-8") as file:
                words_to_delete_posts = [line.strip() for line in file.readlines() if line.strip()]

            # Prepare a query to delete records containing these words in posts.message
            query_posts = f"""
            DELETE FROM posts
            WHERE message IS NULL OR TRIM(message) = ''
                OR TRIM(REGEXP_REPLACE(message, '[[:space:]]+', ' ')) = ''
                OR TRIM(REGEXP_REPLACE(message, '[[:punct:][:space:]]+', '')) = ''
                OR (
                {" OR ".join([f"LOWER(message) LIKE LOWER(%s)" for _ in words_to_delete_posts])}
            )
            """
            
            params_posts = tuple(f"%{word}%" for word in words_to_delete_posts)

            # Execute the query for posts
            posts_success, posts_results = self.db_manager.execute_transaction([query_posts], [params_posts])

            if not posts_success:
                return False, posts_results

            # Read words from word_comments.txt for sub_comments and comments
            word_file_path_comments = "Data/word_comments.txt"
            if not os.path.exists(word_file_path_comments):
                return False, f"File {word_file_path_comments} does not exist"

            with open(word_file_path_comments, "r", encoding="utf-8") as file:
                words_to_delete_comments = [line.strip() for line in file.readlines() if line.strip()]

            # Prepare queries for sub_comments and comments
            query_comments = f"""
            DELETE FROM sub_comments
            WHERE message IS NULL OR TRIM(message) = ''
                OR TRIM(REGEXP_REPLACE(message, '[[:space:]]+', ' ')) = ''
                OR TRIM(REGEXP_REPLACE(message, '[[:punct:][:space:]]+', '')) = ''
                OR (
                {" OR ".join([f"LOWER(message) LIKE LOWER(%s)" for _ in words_to_delete_comments])}
            )
            """
            
            query_comments_2 = f"""
            DELETE FROM comments
            WHERE message IS NULL OR TRIM(message) = ''
                OR TRIM(REGEXP_REPLACE(message, '[[:space:]]+', ' ')) = ''
                OR TRIM(REGEXP_REPLACE(message, '[[:punct:][:space:]]+', '')) = ''
                OR (
                {" OR ".join([f"LOWER(message) LIKE LOWER(%s)" for _ in words_to_delete_comments])}
            )
            """
            
            params_comments = tuple(f"%{word}%" for word in words_to_delete_comments)

            # Execute the queries for sub_comments and comments
            comments_success, comments_results = self.db_manager.execute_transaction(
                [query_comments, query_comments_2], 
                [params_comments, params_comments]
            )

            if not comments_success:
                return False, comments_results

            # Delete duplicate posts
            duplicate_query = """
            DELETE p1
            FROM posts p1
            INNER JOIN posts p2
            ON p1.message = p2.message
            WHERE p1.id > p2.id
            """
            
            duplicate_success_posts, duplicate_results_posts = self.db_manager.execute_transaction([duplicate_query], None)

            if not duplicate_success_posts:
                return False, {"posts_duplicates": duplicate_results_posts}

            # Combine results
            deletion_stats = {
                "posts_deleted": posts_results[0],
                "duplicate_posts_deleted": duplicate_results_posts[0],
                "sub_comments_deleted": comments_results[0],
                "comments_deleted": comments_results[1]
            }

            return True, deletion_stats

        except Exception as e:
            print(str(e))
            return False, str(e)
    
    def fix_messages(self):
        """Fix all records by replacing newline characters with the HTML <br /> tag."""
        queries = [
            """
            UPDATE sub_comments 
            SET message = REPLACE(REPLACE(message, '\r\n', '<br />'), '\n', '<br />')
            WHERE message LIKE '%\n%' OR message LIKE '%\r\n%'
            """,
            """
            UPDATE comments 
            SET message = REPLACE(REPLACE(message, '\r\n', '<br />'), '\n', '<br />')
            WHERE message LIKE '%\n%' OR message LIKE '%\r\n%'
            """,
            """
            UPDATE posts 
            SET message = REPLACE(REPLACE(message, '\r\n', '<br />'), '\n', '<br />')
            WHERE message LIKE '%\n%' OR message LIKE '%\r\n%'
            """
        ]

        success, results = self.db_manager.execute_transaction(queries)

        if not success:
            return False, results

        fix_stats = {
            "sub_comments_fixed": results[0],
            "comments_fixed": results[1],
            "posts_fixed": results[2]
        }

        return True, fix_stats
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate the similarity ratio between two texts."""
        if not text1 or not text2:
            return 0.0
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
    
    def get_all_posts_with_messages(self) -> Tuple[bool, List[Dict]]:
        """Retrieve all posts with their messages and IDs."""
        query = """
        SELECT id, message, LENGTH(message) as msg_length 
        FROM posts 
        WHERE message IS NOT NULL 
        ORDER BY LENGTH(message) DESC
        """
        return self.db_manager.execute_select(query)
    
    def delete_post_and_related(self, post_id: str) -> Tuple[bool, Dict]:
        query = """
        DELETE FROM posts 
        WHERE id = %s
        """

        params_list = [(str(post_id),)]
        
        return self.db_manager.execute_transaction([query], params_list)
    
    def merge_similar_messages(self, messages: List[str]) -> str:
        """
        Gộp các đoạn văn tương tự thành một đoạn văn duy nhất.
        Giữ lại các thông tin độc đáo từ mỗi đoạn.
        """
        if not messages:
            return ""
        
        # Nếu chỉ có một đoạn văn, trả về nguyên bản
        if len(messages) == 1:
            return messages[0]
        
        # Tách các đoạn văn thành câu
        def split_into_sentences(text):
            # Thay thế <br /> bằng dấu chấm để tách câu
            text = re.sub(r'<br\s*/?>', '. ', text, flags=re.IGNORECASE)
            # Tách câu dựa trên dấu chấm, chấm hỏi, chấm than
            sentences = re.split(r'[.!?]+', text)
            return [s.strip() for s in sentences if s.strip()]

        # Tách tất cả đoạn văn thành câu
        all_sentences = []
        for message in messages:
            all_sentences.extend(split_into_sentences(message))
        
        # Loại bỏ các câu trùng lặp hoặc rất giống nhau
        unique_sentences = []
        for sentence in all_sentences:
            is_unique = True
            for existing in unique_sentences:
                if self.calculate_similarity(sentence, existing) > 0.8:  # Ngưỡng 80% cho câu
                    is_unique = False
                    break
            if is_unique and sentence:
                unique_sentences.append(sentence)
        
        # Ghép các câu lại thành đoạn văn
        merged_text = '. '.join(unique_sentences)
        if merged_text and not merged_text.endswith(('.', '!', '?')):
            merged_text += '.'
        
        return merged_text

    def find_and_merge_similar_posts(self) -> Tuple[bool, Dict]:
        try:
            # Lấy tất cả bài viết
            success, posts = self.get_all_posts_with_messages()
            if not success:
                return False, {"error": "Lỗi khi lấy danh sách bài viết", "details": posts}

            total_posts = len(posts)
            posts_processed = 0  # Posts that were deleted
            posts_summarized = 0  # Posts that were kept and updated
            merged_groups = []
            processed_ids = set()
            
            # Chuẩn bị dữ liệu để sắp xếp
            # Chuyển danh sách posts thành danh sách các tuple (id, message, normalized_message)
            # normalized_message là phiên bản đơn giản hóa của message để so sánh hiệu quả hơn
            normalized_posts = []
            for post in posts:
                post_id = str(post['id'])
                message = post['message']
                if message:
                    # Chuẩn hóa tin nhắn: loại bỏ dấu, chuyển thành chữ thường, loại bỏ khoảng trắng thừa
                    normalized_message = unidecode(message.lower())
                    normalized_message = re.sub(r'[^\w\s]', '', normalized_message)
                    normalized_message = re.sub(r'\s+', ' ', normalized_message).strip()
                    normalized_posts.append((post_id, message, normalized_message))
            
            # Sắp xếp các bài viết theo nội dung chuẩn hóa
            normalized_posts.sort(key=lambda x: x[2])
            
            # Áp dụng kỹ thuật 2 pointer
            i = 0
            while i < len(normalized_posts):
                if normalized_posts[i][0] in processed_ids:
                    i += 1
                    continue
                    
                current_post_id = normalized_posts[i][0]
                current_message = normalized_posts[i][1]
                current_normalized = normalized_posts[i][2]
                
                similar_posts = [(current_post_id, current_message)]
                similar_ids = {current_post_id}
                
                # Pointer thứ hai để so sánh với các bài viết phía sau
                j = i + 1
                while j < len(normalized_posts):
                    compare_post_id = normalized_posts[j][0]
                    
                    if compare_post_id in processed_ids:
                        j += 1
                        continue
                        
                    compare_message = normalized_posts[j][1]
                    compare_normalized = normalized_posts[j][2]
                    
                    # Kiểm tra nhanh: nếu độ dài chuỗi khác nhau quá nhiều, bỏ qua
                    length_ratio = min(len(current_normalized), len(compare_normalized)) / max(len(current_normalized), len(compare_normalized))
                    if length_ratio < 0.7:
                        # Nếu chuỗi tiếp theo có thể khác hoàn toàn, thoát khỏi vòng lặp
                        if j > i + 5:  # Kiểm tra 5 bài viết gần nhất
                            break
                        j += 1
                        continue
                    
                    # Tính độ tương đồng chỉ khi có khả năng giống nhau
                    similarity = self.calculate_similarity(current_message, compare_message)
                    
                    if similarity >= 0.8:
                        similar_posts.append((compare_post_id, compare_message))
                        similar_ids.add(compare_post_id)
                    
                    j += 1
                
                # Nếu tìm thấy các bài tương tự
                if len(similar_posts) > 1:
                    messages = [post[1] for post in similar_posts]
                    merged_content = self.merge_similar_messages(messages)

                    # Cập nhật bài viết đầu tiên với nội dung đã gộp
                    update_query = """
                    UPDATE posts 
                    SET message = %s 
                    WHERE id = %s
                    """
                    success, _ = self.db_manager.execute_transaction(
                        [update_query],
                        [(merged_content, current_post_id)]
                    )

                    if not success:
                        i += 1
                        continue

                    # Đếm số bài viết được giữ lại và cập nhật
                    posts_summarized += 1

                    # Xóa các bài viết còn lại
                    for post_id, _ in similar_posts[1:]:
                        delete_success, _ = self.delete_post_and_related(post_id)
                        if delete_success:
                            posts_processed += 1  # Đếm số bài viết bị xóa

                    merged_groups.append({
                        'kept_id': current_post_id,
                        'merged_ids': [post_id for post_id, _ in similar_posts[1:]],
                        'merged_content': merged_content
                    })

                    processed_ids.update(similar_ids)
                
                i += 1

            return True, {
                "total_posts": total_posts,
                "posts_summarized": posts_summarized,  # Số bài viết được giữ lại và cập nhật
                "posts_deleted": posts_processed,      # Số bài viết bị xóa
                "merged_groups": merged_groups
            }

        except Exception as e:
            return False, {"error": str(e)}
            
    def is_fancy_font(self, char):
        """Check if a character belongs to one of the defined fancy font ranges."""
        code_point = ord(char)
        for font_group in self.fancy_fonts.values():
            for start, end in font_group:
                if start <= code_point <= end:
                    return True
        return False
    
    def normalize_text(self, text):
        """Normalize fancy Unicode characters to their basic Latin equivalents."""
        if not text:
            return text
            
        # First normalize combining characters
        normalized = unicodedata.normalize('NFKD', text)
        
        # Replace fancy characters
        result = ""
        for char in normalized:
            if self.is_fancy_font(char):
                # Try to map to basic Latin character
                try:
                    # Using NFKC to convert mathematical symbols to regular characters
                    basic_char = unicodedata.normalize('NFKC', char)
                    # If it's still fancy, use the Latin equivalent if possible
                    if self.is_fancy_font(basic_char):
                        char_name = unicodedata.name(char, '').lower()
                        if 'letter' in char_name:
                            if 'capital' in char_name or 'upper' in char_name:
                                for c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
                                    if c.lower() in char_name:
                                        result += c
                                        break
                                else:
                                    result += char  # Keep original if no match
                            else:
                                for c in 'abcdefghijklmnopqrstuvwxyz':
                                    if c in char_name:
                                        result += c
                                        break
                                else:
                                    result += char  # Keep original if no match
                        elif 'digit' in char_name:
                            for d in '0123456789':
                                if d in char_name:
                                    result += d
                                    break
                            else:
                                result += char  # Keep original if no match
                        else:
                            result += basic_char
                    else:
                        result += basic_char
                except:
                    result += char  # If anything fails, keep the original character
            else:
                result += char
                
        # Handle special cases like combining accents that may have been missed
        result = unicodedata.normalize('NFC', result)
        
        # Fix common special characters
        result = result.replace('́', "'")  # Fix some apostrophes
        
        return result
    
    def fix_font_inconsistencies(self):
        """Fix font inconsistencies in all text fields of the database."""
        try:
            # Get samples of text with potential fancy fonts
            sample_query = """
            SELECT id, message FROM posts 
            WHERE message REGEXP '[^\u0000-\u007F]' 
            LIMIT 1000
            """
            
            success, samples = self.db_manager.execute_select(sample_query)
            if not success:
                return False, {"error": "Failed to retrieve text samples"}
            
            # Count initial records with inconsistent fonts
            count_query = """
            SELECT 
                (SELECT COUNT(*) FROM posts WHERE message REGEXP '[^\u0000-\u007F]') as posts_count,
                (SELECT COUNT(*) FROM comments WHERE message REGEXP '[^\u0000-\u007F]') as comments_count,
                (SELECT COUNT(*) FROM sub_comments WHERE message REGEXP '[^\u0000-\u007F]') as subcomments_count
            """
            
            success, counts = self.db_manager.execute_select(count_query)
            if not success:
                return False, {"error": "Failed to count records with inconsistent fonts"}
            
            initial_counts = counts[0] if counts else {"posts_count": 0, "comments_count": 0, "subcomments_count": 0}
            
            # Process and fix messages in batches
            post_query = """
            UPDATE posts 
            SET message = %s 
            WHERE id = %s
            """
            
            comment_query = """
            UPDATE comments 
            SET message = %s 
            WHERE id = %s
            """
            
            subcomment_query = """
            UPDATE sub_comments 
            SET message = %s 
            WHERE id = %s
            """
            
            # Process posts in batches
            posts_batch_size = 1000
            posts_fixed = 0
            
            for offset in range(0, initial_counts["posts_count"], posts_batch_size):
                batch_query = f"""
                SELECT id, message FROM posts 
                WHERE message REGEXP '[^\u0000-\u007F]'
                LIMIT {posts_batch_size} OFFSET {offset}
                """
                
                success, posts_batch = self.db_manager.execute_select(batch_query)
                if not success:
                    continue
                
                batch_updates = []
                batch_params = []
                
                for post in posts_batch:
                    original_text = post['message']
                    normalized_text = self.normalize_text(original_text)
                    
                    if normalized_text != original_text:
                        batch_updates.append(post_query)
                        batch_params.append((normalized_text, post['id']))
                
                if batch_updates:
                    success, results = self.db_manager.execute_transaction(batch_updates, batch_params)
                    if success:
                        posts_fixed += len(results)
            
            # Process comments in batches
            comments_batch_size = 1000
            comments_fixed = 0
            
            for offset in range(0, initial_counts["comments_count"], comments_batch_size):
                batch_query = f"""
                SELECT id, message FROM comments 
                WHERE message REGEXP '[^\u0000-\u007F]'
                LIMIT {comments_batch_size} OFFSET {offset}
                """
                
                success, comments_batch = self.db_manager.execute_select(batch_query)
                if not success:
                    continue
                
                batch_updates = []
                batch_params = []
                
                for comment in comments_batch:
                    original_text = comment['message']
                    normalized_text = self.normalize_text(original_text)
                    
                    if normalized_text != original_text:
                        batch_updates.append(comment_query)
                        batch_params.append((normalized_text, comment['id']))
                
                if batch_updates:
                    success, results = self.db_manager.execute_transaction(batch_updates, batch_params)
                    if success:
                        comments_fixed += len(results)
            
            # Process sub_comments in batches
            subcomments_batch_size = 1000
            subcomments_fixed = 0
            
            for offset in range(0, initial_counts["subcomments_count"], subcomments_batch_size):
                batch_query = f"""
                SELECT id, message FROM sub_comments 
                WHERE message REGEXP '[^\u0000-\u007F]'
                LIMIT {subcomments_batch_size} OFFSET {offset}
                """
                
                success, subcomments_batch = self.db_manager.execute_select(batch_query)
                if not success:
                    continue
                
                batch_updates = []
                batch_params = []
                
                for subcomment in subcomments_batch:
                    original_text = subcomment['message']
                    normalized_text = self.normalize_text(original_text)
                    
                    if normalized_text != original_text:
                        batch_updates.append(subcomment_query)
                        batch_params.append((normalized_text, subcomment['id']))
                
                if batch_updates:
                    success, results = self.db_manager.execute_transaction(batch_updates, batch_params)
                    if success:
                        subcomments_fixed += len(results)
            
            # Sample results for demonstration
            sample_results = []
            for i, sample in enumerate(samples[:5]):  # Just show first 5 examples
                if sample['message']:
                    original = sample['message']
                    normalized = self.normalize_text(original)
                    if original != normalized:
                        sample_results.append({
                            'id': sample['id'],
                            'original': original[:100] + '...' if len(original) > 100 else original,
                            'normalized': normalized[:100] + '...' if len(normalized) > 100 else normalized
                        })
            
            return True, {
                "initial_counts": initial_counts,
                "posts_fixed": posts_fixed,
                "comments_fixed": comments_fixed,
                "subcomments_fixed": subcomments_fixed,
                "sample_results": sample_results
            }
            
        except Exception as e:
            return False, {"error": str(e)}