import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../constants/colors.dart';
import '../constants/images.dart';
import '../components/custom_button.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _showPassword = false;
  String _emailError = '';
  String _passwordError = '';

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.isAuthenticated) {
      // Navigate to main tabs
      Future.microtask(
          () => Navigator.of(context).pushReplacementNamed('/main'));
    }
  }

  bool _validateEmail(String email) {
    final emailRegex = RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$');
    if (email.isEmpty) {
      setState(() {
        _emailError = 'Email is required';
      });
      return false;
    } else if (!emailRegex.hasMatch(email)) {
      setState(() {
        _emailError = 'Please enter a valid email address';
      });
      return false;
    }
    setState(() {
      _emailError = '';
    });
    return true;
  }

  bool _validatePassword(String password) {
    if (password.isEmpty) {
      setState(() {
        _passwordError = 'Password is required';
      });
      return false;
    } else if (password.length < 6) {
      setState(() {
        _passwordError = 'Password must be at least 6 characters';
      });
      return false;
    }
    setState(() {
      _passwordError = '';
    });
    return true;
  }

  Future<void> _handleGoogleSignIn() async {
    debugPrint('Log in with Google pressed');
    // Implement Google Sign-In
  }

  Future<void> _handleLogin() async {
    final isEmailValid = _validateEmail(_emailController.text);
    final isPasswordValid = _validatePassword(_passwordController.text);

    if (!isEmailValid || !isPasswordValid) {
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    try {
      final success = await authProvider.login(
        _emailController.text,
        _passwordController.text,
      );
      if (success) {
        // Clear input fields
        _emailController.clear();
        _passwordController.clear();

        // Navigate to main screen
        Navigator.of(context).pushReplacementNamed('/main');
        debugPrint('Login successful');
      } else {
        _showErrorDialog('Login Failed',
            authProvider.error ?? 'Something went wrong. Please try again.');
      }
    } catch (error) {
      _showErrorDialog(
          'Login Failed', 'Something went wrong. Please try again.');
    }
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _handleForgotPassword() {
    // Navigate to ForgotPassword screen
    // Navigator.of(context).pushNamed('/forgot-password');
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isLoading = authProvider.isLoading;
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Column(
            children: [
              // Back button
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                child: Row(
                  children: [
                    InkWell(
                      onTap: () => Navigator.of(context).pop(),
                      child: Row(
                        children: [
                          const Icon(Icons.chevron_left, size: 24),
                          const SizedBox(width: 10),
                          Text(
                            'Log In',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w500,
                              color: AppColors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Main content
              Expanded(
                child: SingleChildScrollView(
                  padding:
                      EdgeInsets.symmetric(horizontal: screenSize.width * 0.06),
                  child: Column(
                    children: [
                      SizedBox(height: screenSize.height * 0.02),
                      Text(
                        'Enter your info to log in',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: screenSize.height * 0.03),

                      // Email input
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 56,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: _emailError.isNotEmpty
                                    ? Colors.red
                                    : Gray.border,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: TextField(
                              controller: _emailController,
                              decoration: InputDecoration(
                                hintText: 'Email',
                                hintStyle: TextStyle(color: Gray.text),
                                contentPadding:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                border: InputBorder.none,
                              ),
                              keyboardType: TextInputType.emailAddress,
                              autocorrect: false,
                              enabled: !isLoading,
                              onChanged: (text) {
                                if (_emailError.isNotEmpty) {
                                  _validateEmail(text);
                                }
                              },
                            ),
                          ),
                          if (_emailError.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 5, left: 5),
                              child: Text(
                                _emailError,
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Password input
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 56,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: _passwordError.isNotEmpty
                                    ? Colors.red
                                    : Gray.border,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Stack(
                              alignment: Alignment.centerRight,
                              children: [
                                TextField(
                                  controller: _passwordController,
                                  decoration: InputDecoration(
                                    hintText: 'Password',
                                    hintStyle: TextStyle(color: Gray.text),
                                    contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 20),
                                    border: InputBorder.none,
                                  ),
                                  obscureText: !_showPassword,
                                  autocorrect: false,
                                  enabled: !isLoading,
                                  onChanged: (text) {
                                    if (_passwordError.isNotEmpty) {
                                      _validatePassword(text);
                                    }
                                  },
                                ),
                                Positioned(
                                  right: 20,
                                  child: GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _showPassword = !_showPassword;
                                      });
                                    },
                                    child: Icon(
                                      _showPassword
                                          ? Icons.visibility_off
                                          : Icons.visibility,
                                      color: Gray.text,
                                      size: 24,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (_passwordError.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 5, left: 5),
                              child: Text(
                                _passwordError,
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                        ],
                      ),

                      // Forgot password
                      // Align(
                      //   alignment: Alignment.centerRight,
                      //   child: TextButton(
                      //     onPressed: isLoading ? null : _handleForgotPassword,
                      //     child: Text(
                      //       'Forgot Password?',
                      //       style: TextStyle(
                      //         color: AppColors.primary,
                      //         fontSize: 14,
                      //       ),
                      //     ),
                      //   ),
                      // ),

                      // Login button
                      SizedBox(height: screenSize.height * 0.02),
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: CustomButton(
                          title: isLoading ? 'Logging In...' : 'Log In',
                          variant: ButtonVariant.primary,
                          onPress: isLoading ? () {} : _handleLogin,
                        ),
                      ),

                      // Illustration
                      SizedBox(height: 20),
                      Container(
                        height: screenSize.height * 0.25,
                        alignment: Alignment.center,
                        child: Image.asset(
                          AppImages.loginIllustration,
                          fit: BoxFit.contain,
                        ),
                      ),

                      // Google button
                      SizedBox(height: 20),
                      // Padding(
                      //   padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
                      //   child: SizedBox(
                      //     width: double.infinity,
                      //     height: 56,
                      //     child: CustomButton(
                      //       title: isLoading ? 'Please wait...' : 'Continue with Google',
                      //       variant: ButtonVariant.google,
                      //       iconPath: AppImages.googleIcon,
                      //       onPress: isLoading ? () {} : _handleGoogleSignIn,
                      //     ),
                      //   ),
                      // ),

                      // Sign up link
                      SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "Don't have an account? ",
                            style: TextStyle(
                              color: Gray.text,
                              fontSize: 14,
                            ),
                          ),
                          GestureDetector(
                            onTap: isLoading
                                ? null
                                : () =>
                                    Navigator.of(context).pushNamed('/signup'),
                            child: Text(
                              'Sign Up',
                              style: TextStyle(
                                color: AppColors.primary,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 30),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
